/**
 * VoIP消息处理器
 * 处理VoIP相关的消息
 * 
 * <AUTHOR>
 */

import Foundation
import SwiftUI

/**
 * VoIP消息处理器类
 * 实现VoIP相关功能的消息处理
 */
class VoIPMessageHandler: MessageHandler {
    
    var handlerName: String = "VoIPMessageHandler"
    
    var supportedActions: [String] = [
        "voip.call",
        "voip.hangup",
        "voip.answer",
        "voip.reject",
        "voip.getStatus",
        "voip.login",
        "voip.logout"
    ]
    
    func handleMessage(_ message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[VoIPMessageHandler] 处理消息: \(message.action)")
        
        let (_, method) = MessageProtocol.shared.parseAction(message.action)
        
        switch method {
        case "call":
            handleVoIPCall(message: message, completion: completion)
        case "hangup":
            handleVoIPHangup(message: message, completion: completion)
        case "answer":
            handleVoIPAnswer(message: message, completion: completion)
        case "reject":
            handleVoIPReject(message: message, completion: completion)
        case "getStatus":
            handleGetVoIPStatus(message: message, completion: completion)
        case "login":
            handleVoIPLogin(message: message, completion: completion)
        case "logout":
            handleVoIPLogout(message: message, completion: completion)
        default:
            completion(.failure(error: "不支持的VoIP操作: \(method)"))
        }
    }
    
    // MARK: - VoIP操作处理
    
    /**
     * 处理VoIP呼叫
     */
    private func handleVoIPCall(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        guard let number = message.data["number"] as? String, !number.isEmpty else {
            completion(.failure(error: "VoIP呼叫缺少号码参数"))
            return
        }
        
        let token = message.data["token"] as? String
        let displayName = message.data["displayName"] as? String
        
        print("[VoIPMessageHandler] 处理VoIP呼叫请求 - 号码: \(number), Token: \(token ?? "无")")
        
        // 在主线程中处理UI相关操作
        DispatchQueue.main.async {
            self.initiateVoIPCall(number: number, token: token, displayName: displayName, completion: completion)
        }
    }
    
    /**
     * 发起VoIP呼叫
     */
    private func initiateVoIPCall(
        number: String,
        token: String?,
        displayName: String?,
        completion: @escaping (MessageHandlerResult) -> Void
    ) {
        print("[VoIPMessageHandler] 通过消息处理器发起VoIP呼叫: \(number)")
        
        // 设置呼叫号码到CallViewModel
        CallViewModel.shared.callNum = number
        
        // 如果有token，可以在这里处理认证逻辑
        if let token = token {
            print("[VoIPMessageHandler] 使用Token进行认证: \(token)")
            // 这里可以添加token验证逻辑
        }
        
        // 检查VoIP登录状态，如果未登录则先登录
        if !CallViewModel.shared.loggedIn {
            print("[VoIPMessageHandler] VoIP未登录，尝试使用当前用户信息登录")
            
            // 获取当前用户的VoIP账号信息
            if let userData = UserManager.shared.getUser(),
               let voipNumber = userData.member.voipNumber,
               let voipPassword = userData.member.voipPassword {
                print("[VoIPMessageHandler] 找到VoIP账号信息，开始登录: \(voipNumber)")
                
                // 设置账号信息并登录
                CallViewModel.shared.username = voipNumber
                CallViewModel.shared.passwd = voipPassword
                CallViewModel.shared.login()
                
                // 等待登录完成后再发起呼叫
                waitForVoIPLogin { success in
                    if success {
                        self.performVoIPCall(number: number, token: token, completion: completion)
                    } else {
                        completion(.failure(error: "VoIP登录失败，无法发起呼叫"))
                    }
                }
            } else {
                print("[VoIPMessageHandler] 未找到VoIP账号信息，无法发起呼叫")
                completion(.failure(error: "VoIP账号未配置，无法发起呼叫"))
                return
            }
        } else {
            // 已登录，直接发起呼叫
            performVoIPCall(number: number, token: token, completion: completion)
        }
    }
    
    /**
     * 执行VoIP呼叫
     */
    private func performVoIPCall(
        number: String,
        token: String?,
        completion: @escaping (MessageHandlerResult) -> Void
    ) {
        print("[VoIPMessageHandler] 执行VoIP呼叫: \(number)")

        // 设置呼叫号码
        CallViewModel.shared.callNum = number

        // 发起呼叫
        CallViewModel.shared.outgoingCall()

        // 使用模态方式弹出VoIP呼叫界面，类似WiFi配置
        presentVoIPCallInterface(for: .outgoing)

        // 返回成功结果
        let responseData: [String: Any] = [
            "number": number,
            "callId": UUID().uuidString,
            "timestamp": Date().timeIntervalSince1970
        ]

        completion(.success(data: responseData))
    }

    /**
     * 以模态方式弹出VoIP通话界面
     */
    private func presentVoIPCallInterface(for callType: VoIPCallType) {
        // 获取当前的根视图控制器
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootViewController = window.rootViewController else {
            print("[VoIPMessageHandler] 无法获取根视图控制器")
            return
        }

        // 创建VoIP通话视图
        let voipCallView = createVoIPCallView(for: callType)

        // 包装为UIHostingController
        let hostingController = UIHostingController(rootView: voipCallView)
        hostingController.modalPresentationStyle = .fullScreen

        // 呈现VoIP通话页面
        rootViewController.present(hostingController, animated: true) {
            print("[VoIPMessageHandler] VoIP通话页面已打开: \(callType)")
        }
    }

    /**
     * 创建VoIP通话视图
     */
    private func createVoIPCallView(for callType: VoIPCallType) -> some View {
        return VoIPCallContainerView(callType: callType)
    }
    
    /**
     * 处理VoIP挂断
     */
    private func handleVoIPHangup(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[VoIPMessageHandler] 处理VoIP挂断请求")
        
        DispatchQueue.main.async {
            // 执行挂断操作
            CallViewModel.shared.terminateCall()
            
            let responseData: [String: Any] = [
                "action": "hangup",
                "timestamp": Date().timeIntervalSince1970
            ]
            
            completion(.success(data: responseData))
        }
    }
    
    /**
     * 处理VoIP接听
     */
    private func handleVoIPAnswer(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[VoIPMessageHandler] 处理VoIP接听请求")
        
        DispatchQueue.main.async {
            // 执行接听操作
            CallViewModel.shared.acceptCall()
            
            let responseData: [String: Any] = [
                "action": "answer",
                "timestamp": Date().timeIntervalSince1970
            ]
            
            completion(.success(data: responseData))
        }
    }
    
    /**
     * 处理VoIP拒绝
     */
    private func handleVoIPReject(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[VoIPMessageHandler] 处理VoIP拒绝请求")
        
        DispatchQueue.main.async {
            // 执行拒绝操作 - 使用terminateCall来拒绝来电
            CallViewModel.shared.terminateCall()

            let responseData: [String: Any] = [
                "action": "reject",
                "timestamp": Date().timeIntervalSince1970
            ]

            completion(.success(data: responseData))
        }
    }
    
    /**
     * 处理获取VoIP状态
     */
    private func handleGetVoIPStatus(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[VoIPMessageHandler] 处理获取VoIP状态请求")
        
        let callViewModel = CallViewModel.shared
        let responseData: [String: Any] = [
            "isLoggedIn": callViewModel.loggedIn,
            "isInCall": callViewModel.mCore.currentCall != nil,
            "username": callViewModel.username,
            "accountState": callViewModel.mCore.defaultAccount?.state.rawValue ?? -1,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        completion(.success(data: responseData))
    }
    
    /**
     * 处理VoIP登录
     */
    private func handleVoIPLogin(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        guard let username = message.data["username"] as? String,
              let password = message.data["password"] as? String else {
            completion(.failure(error: "VoIP登录缺少用户名或密码"))
            return
        }
        
        print("[VoIPMessageHandler] 处理VoIP登录请求: \(username)")
        
        DispatchQueue.main.async {
            CallViewModel.shared.username = username
            CallViewModel.shared.passwd = password
            CallViewModel.shared.login()
            
            // 等待登录结果
            self.waitForVoIPLogin { success in
                if success {
                    let responseData: [String: Any] = [
                        "action": "login",
                        "username": username,
                        "timestamp": Date().timeIntervalSince1970
                    ]
                    completion(.success(data: responseData))
                } else {
                    completion(.failure(error: "VoIP登录失败"))
                }
            }
        }
    }
    
    /**
     * 处理VoIP登出
     */
    private func handleVoIPLogout(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[VoIPMessageHandler] 处理VoIP登出请求")
        
        DispatchQueue.main.async {
            CallViewModel.shared.unregister()
            
            let responseData: [String: Any] = [
                "action": "logout",
                "timestamp": Date().timeIntervalSince1970
            ]
            
            completion(.success(data: responseData))
        }
    }
    
    // MARK: - 辅助方法
    
    /**
     * 等待VoIP登录完成
     */
    private func waitForVoIPLogin(completion: @escaping (Bool) -> Void) {
        let maxWaitTime = 5.0 // 最大等待5秒
        let checkInterval = 0.1 // 每100ms检查一次
        var elapsedTime = 0.0
        
        func checkLoginStatus() {
            if CallViewModel.shared.loggedIn && CallViewModel.shared.mCore.defaultAccount?.state == .Ok {
                print("[VoIPMessageHandler] VoIP登录成功")
                completion(true)
            } else if elapsedTime >= maxWaitTime {
                print("[VoIPMessageHandler] VoIP登录超时")
                completion(false)
            } else {
                elapsedTime += checkInterval
                DispatchQueue.main.asyncAfter(deadline: .now() + checkInterval) {
                    checkLoginStatus()
                }
            }
        }
        
        checkLoginStatus()
    }
}

// MARK: - VoIP通话类型枚举

enum VoIPCallType {
    case outgoing
    case incoming
    case connected
}

// MARK: - VoIP通话容器视图

struct VoIPCallContainerView: View {
    let callType: VoIPCallType
    @ObservedObject private var callVM = CallViewModel.shared
    @Environment(\.presentationMode) var presentationMode
    @State private var currentCallType: VoIPCallType

    init(callType: VoIPCallType) {
        self.callType = callType
        self._currentCallType = State(initialValue: callType)
    }

    var body: some View {
        ZStack {
            // 根据当前通话状态显示不同的界面
            switch currentCallType {
            case .outgoing:
                CallOutgoingView(viewModel: callVM)
            case .incoming:
                CallIncomingView(viewModel: callVM)
            case .connected:
                CallConnectView(viewModel: callVM)
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .callConnected)) { _ in
            // 只有在真正建立通话连接时才切换到通话界面
            // 对于外呼：需要isCalling=true且isOutgoingCallRunning=true
            // 对于来电：需要isCallIncoming=true且isIncomingCallRunning=true
            let shouldShowConnectedView = (callVM.isCalling && callVM.isOutgoingCallRunning) ||
                                        (callVM.isCallIncoming && callVM.isIncomingCallRunning)

            print("[VoIPCallContainerView] 收到通话连接通知")
            print("  - isCalling: \(callVM.isCalling)")
            print("  - isOutgoingCallRunning: \(callVM.isOutgoingCallRunning)")
            print("  - isCallIncoming: \(callVM.isCallIncoming)")
            print("  - isIncomingCallRunning: \(callVM.isIncomingCallRunning)")
            print("  - shouldShowConnectedView: \(shouldShowConnectedView)")
            print("  - currentCallType: \(currentCallType)")

            if shouldShowConnectedView {
                print("[VoIPCallContainerView] 切换到通话界面")
                currentCallType = .connected
            } else {
                print("[VoIPCallContainerView] 通话状态不满足切换条件，保持当前界面")
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .callEnded)) { _ in
            // 通话结束时，直接dismiss回到H5页面，类似WiFi配置
            print("[VoIPCallContainerView] 通话结束，关闭界面")
            presentationMode.wrappedValue.dismiss()
        }
    }
}
