//
//  DependencyContainer.swift
//  wadar
//
//  Created by guan on 2025/6/26.
//

import Foundation

/// 依赖注入容器，用于管理模块间的依赖关系
class DependencyContainer {
    static let shared = DependencyContainer()
    
    private var services: [String: Any] = [:]
    
    private init() {
        setupDefaultServices()
    }
    
    /// 注册服务
    func register<T>(_ service: T, for type: T.Type) {
        let key = String(describing: type)
        services[key] = service
    }
    
    /// 解析服务
    func resolve<T>(_ type: T.Type) -> T? {
        let key = String(describing: type)
        return services[key] as? T
    }
    
    /// 设置默认服务
    private func setupDefaultServices() {
        // 注册Common模块的服务
        register(AppConfig.shared, for: ConfigManagerProtocol.self)
        register(NetworkManager.shared, for: NetworkManagerProtocol.self)
        register(UserManager.shared, for: UserManagerProtocol.self)
        register(NavigationManager.shared, for: (any NavigationManagerProtocol).self)
    }
}

/// 依赖注入属性包装器
@propertyWrapper
struct Injected<T> {
    private let type: T.Type
    
    init(_ type: T.Type) {
        self.type = type
    }
    
    var wrappedValue: T {
        guard let service = DependencyContainer.shared.resolve(type) else {
            fatalError("无法解析依赖: \(type)")
        }
        return service
    }
}

/// 可选依赖注入属性包装器
@propertyWrapper
struct OptionalInjected<T> {
    private let type: T.Type
    
    init(_ type: T.Type) {
        self.type = type
    }
    
    var wrappedValue: T? {
        return DependencyContainer.shared.resolve(type)
    }
}
