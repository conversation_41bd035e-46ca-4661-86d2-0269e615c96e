import SwiftUI

struct CallConnectView: View {
    @ObservedObject var viewModel : CallViewModel
    @State private var callDuration: TimeInterval = 0
    @State private var timer: Timer? = nil
    @State private var isMicrophoneOn: Bool = true
    @State private var isSpeakerOn: Bool = false
    @State private var displayName: String = ""
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        ZStack {
            Color("call_background")
                .edgesIgnoringSafeArea(.all)
            VStack {
                VStack {
                    // 头像显示
                    Image(systemName: "person.crop.circle.fill")
                        .resizable()
                        .frame(width: 120, height: 120)
                        .foregroundColor(.blue) // 更明显的头像颜色
                        .background(Color.white)
                        .clipShape(Circle())
                        .padding(.top, 60)
                    // 显示对方号码（被叫号或主叫号）
                    Text(getPeerNumber())
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.black) // 更明显的号码颜色
                        .padding(.top, 20)
                }
                Spacer()
                // 通话时间始终显示
                Text(timeString(from: callDuration))
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(.red) // 更明显的时间颜色
                    .padding(.bottom, 50)
                Spacer()
                HStack(alignment: .bottom, spacing: 20) {
                    Spacer()
                    VStack {
                        Button(action: {
                            viewModel.muteMicrophone()
                            isMicrophoneOn = viewModel.mCore.micEnabled
                        }) {
                            Image(systemName: viewModel.mCore.micEnabled ? "microphone.fill" : "microphone.slash.fill")
                                .resizable()
                                .frame(width: 40, height: 40)
                                .foregroundColor(viewModel.mCore.micEnabled ? .white : .gray)
                                .padding(14)
                                .background(Color.black.opacity(0.2))
                                .clipShape(Circle())
                        }
                        Text(viewModel.mCore.micEnabled ? "麦克风" : "麦克风已关")
                            .font(.system(size: 16))
                            .foregroundColor(.white)
                            .padding(.top, 5)
                    }
                    Spacer(minLength: 10)
                    VStack {
                        Button(action: {
                            hangUpCall()
                        }) {
                            ZStack {
                                Circle()
                                    .fill(Color.red)
                                    .frame(width: 80, height: 80)
                                Image(systemName: "phone.down.fill")
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(width: 40, height: 40)
                                    .foregroundColor(.white)
                            }
                        }
                        Text("挂断")
                            .font(.system(size: 16))
                            .foregroundColor(.red)
                            .padding(.top, 5)
                    }
                    Spacer(minLength: 10)
                    VStack {
                        Button(action: {
                            viewModel.toggleSpeaker()
                            isSpeakerOn = viewModel.isSpeakerEnabled
                        }) {
                            Image(systemName: viewModel.isSpeakerEnabled ? "speaker.wave.2.fill" : "speaker.slash.fill")
                                .resizable()
                                .frame(width: 40, height: 40)
                                .foregroundColor(viewModel.isSpeakerEnabled ? .white : .gray)
                                .padding(14)
                                .background(Color.black.opacity(0.2))
                                .clipShape(Circle())
                        }
                        Text(viewModel.isSpeakerEnabled ? "扬声器开" : "扬声器关")
                            .font(.system(size: 16))
                            .foregroundColor(.white)
                            .padding(.top, 5)
                    }
                    Spacer()
                }
                .padding(.bottom, 80)
            }
        }
        .onAppear {
            startTimer()
            isMicrophoneOn = viewModel.mCore.micEnabled
            isSpeakerOn = viewModel.isSpeakerEnabled
        }
        .onDisappear {
            timer?.invalidate()
        }
    }

    func startTimer() {
        timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { _ in
            callDuration += 1
        }
    }

    func hangUpCall() {
        // 挂断通话逻辑，实际项目中请调用VoIP SDK
        self.viewModel.terminateCall()
        timer?.invalidate()
        // 注释掉presentationMode.dismiss()，让VoIPCallContainerView统一处理界面关闭
        // presentationMode.wrappedValue.dismiss()
    }
    
    // 获取对方号码（外呼时为被叫号，接听时为主叫号）
    func getPeerNumber() -> String {
        if viewModel.isOutgoingCallRunning || viewModel.isCalling {
            // 外呼时显示被叫号码
            return viewModel.callNum.isEmpty ? "未知号码" : viewModel.callNum
        } else if viewModel.isIncomingCallRunning || viewModel.isCallIncoming {
            // 接听时显示主叫号码
            return viewModel.incomingCallRemoteAddress.isEmpty ? "未知号码" : viewModel.incomingCallRemoteAddress
        }
        return "未知号码"
    }

    func timeString(from interval: TimeInterval) -> String {
        let minutes = Int(interval) / 60
        let seconds = Int(interval) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}

#Preview {
    CallConnectView(viewModel: CallViewModel.shared)
}
